'use client';

import { SchemaBuilderStepEnum, useSchemaBuilderContext } from '@/contexts/SchemaBuilderContext';
import { SchemaOfferingNavbar } from './components/SchemaBuilderNavbar';
import { DeploymentType } from '@/types/deployments';
import { useDeploymentDetails } from '@/hooks/useDeploymentDetails';
import { useGetAndSetSchemasOfIssuer } from '@/hooks/useGetAndSetSchemasOfIssuer';
import { useSchemaOfferingForm } from '@/hooks/useSchemaOfferingForm';
import { useGetIssuerOfferings } from '@/hooks/useGetIssuerOfferings';
import { useTranslations } from 'next-intl';
import { ButtonGradient } from '@/components/Buttons';

const TYPE = DeploymentType.ISSUER;

export const SchemaOffering = () => {
    const t = useTranslations('schema_offering');
    const { activeVersionId, setStep } = useSchemaBuilderContext();
    const { data } = useDeploymentDetails({ type: TYPE });
    const { data: schemas = [] } = useGetAndSetSchemasOfIssuer({
        fullHost: data?.fullHost,
        authorization: data?.authKey || undefined,
    });

    const { data: offerings = [], isLoading: isLoadingOfferings } = useGetIssuerOfferings({
        fullHost: data?.fullHost,
        authorization: data?.authKey || undefined,
    });

    const schema = schemas.find(s => s.id === activeVersionId);
    if (!schema) throw new Error('No schema found');

    const { fieldsToRender, onSubmit, isValid, isSubmitting } = useSchemaOfferingForm({
        schema,
    });

    return (
        <div className="px-10 flex flex-col items-start gap-4 h-[calc(100vh_-_200px)]">
            <SchemaOfferingNavbar onBackButtonClick={() => setStep(SchemaBuilderStepEnum.SCHEMA_LIST)} />
            <div className="flex flex-row gap-4 overflow-auto without-scrollbar w-full h-full">
                {/* Form Column */}
                <div className="flex flex-1 flex-col gap-4 bg-main-600/5 rounded-lg p-4">
                    <div className="flex flex-row justify-between items-center">
                        <div>
                            <h1 className="text-xl font-semibold">{t('title')}</h1>
                            <p className="text-sm text-gray-600">
                                {schema.name} - {t('form_description')}
                            </p>
                        </div>
                    </div>

                    {/* Form */}
                    <form onSubmit={onSubmit} className="flex flex-col gap-4 flex-1 overflow-y-auto without-scrollbar">
                        <div className="grid grid-cols-1 gap-4">{fieldsToRender}</div>

                        <div className="flex justify-end pt-4 border-t">
                            <ButtonGradient
                                id="components_SchemaManager_SchemaOffering_button_5wnohp"
                                type="submit"
                                disabled={!isValid}
                                isLoading={isSubmitting}
                            >
                                {t('submit_button')}
                            </ButtonGradient>
                        </div>
                    </form>
                </div>

                {/* Offerings List Column */}
                <div className="flex flex-1 flex-col gap-4 bg-main-600/5 rounded-lg p-4">
                    <h2 className="text-lg font-semibold">{t('offerings_list')}</h2>
                    <div className="flex-1 overflow-y-auto without-scrollbar">
                        {isLoadingOfferings ? (
                            <div className="flex items-center justify-center h-32">
                                <div className="text-sm text-gray-500">{t('loading_offerings')}</div>
                            </div>
                        ) : offerings.length === 0 ? (
                            <div className="flex items-center justify-center h-32">
                                <div className="text-sm text-gray-500">{t('no_offerings')}</div>
                            </div>
                        ) : (
                            <div className="space-y-3">
                                {offerings.map(offering => (
                                    <div
                                        key={offering.id}
                                        className="p-3 border border-gray-200 rounded-lg hover:border-main-100 transition-colors"
                                    >
                                        <div className="flex flex-col gap-2">
                                            <div className="font-medium text-sm truncate">
                                                {offering.display?.name || offering.id}
                                            </div>
                                            {offering.recipient && (
                                                <div className="text-xs text-gray-500">
                                                    Recipient: {offering.recipient.slice(0, 20)}...
                                                </div>
                                            )}
                                            {offering.expires_at && (
                                                <div className="text-xs text-gray-500">
                                                    Expires: {new Date(offering.expires_at).toLocaleDateString()}
                                                </div>
                                            )}
                                            {Object.entries(offering.credential_subject as Record<string, unknown>).map(
                                                ([key, value]) => (
                                                    <div key={key} className="text-xs text-gray-500">
                                                        {key}:{' '}
                                                        {typeof value === 'string' ||
                                                        typeof value === 'number' ||
                                                        typeof value === 'boolean'
                                                            ? String(value)
                                                            : JSON.stringify(value)}
                                                    </div>
                                                )
                                            )}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                </div>

                <div className="flex-1 h-full bg-main-600/5 rounded-lg p-4">QR</div>
            </div>
        </div>
    );
};
